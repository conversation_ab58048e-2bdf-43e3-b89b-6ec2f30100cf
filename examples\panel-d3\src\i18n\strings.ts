export default {
  en: {
    dsc_cancel: 'Cancel',
    dsc_confirm: 'Confirm',
    dsc_save: 'Save',
    dsc_countdown_tips: 'Countdown to {0}',
    dsc_countdown_turn_on: 'turn on',
    dsc_countdown_turn_off: 'turn off',
    dsc_settings: 'Settings',
    dsc_switch: 'Switch',
    dsc_fan: 'Fan',
    dsc_light: 'Light',
    dsc_swing: 'Swing',
    dsc_swing_vertical: 'Vertical',
    dsc_swing_horizontal: 'Horizontal',
    dsc_on: 'ON',
    dsc_off: 'OFF',
    dsc_timing: 'Timing',
    dsc_add: 'Add',
    dsc_add_timing: 'Add timing',
    dsc_edit_timing: 'Edit timing',
    dsc_week_full_mon: 'Mon',
    dsc_week_full_tue: 'Tue',
    dsc_week_full_wed: 'Wed',
    dsc_week_full_thu: 'Thu',
    dsc_week_full_fri: 'Fri',
    dsc_week_full_sat: 'Sat',
    dsc_week_full_sun: 'Sun',
    dsc_one_time: 'One time',
    dsc_everyday: 'Everyday',
    dsc_remark: 'Remark',
    dsc_less_than_20: 'No more than 20 characters',
    dsc_edit_success: 'Successfully modified',
    dsc_create_success: 'Successfully created',
    dsc_no_timing: 'No timing',
    dsc_error: 'Error',
    dsc_am: 'AM',
    dsc_pm: 'PM',
  },
  zh: {
    dsc_cancel: '取消',
    dsc_confirm: '确认',
    dsc_save: '保存',
    dsc_countdown_tips: '倒计时{0}',
    dsc_countdown_turn_on: '打开',
    dsc_countdown_turn_off: '关闭',
    dsc_settings: '设置',
    dsc_switch: '开关',
    dsc_fan: '风扇',
    dsc_light: '灯光',
    dsc_swing: '摆风',
    dsc_swing_vertical: '上下摆风',
    dsc_swing_horizontal: '左右摆风',
    dsc_on: '开',
    dsc_off: '关',
    dsc_timing: '定时',
    dsc_add: '添加',
    dsc_add_timing: '添加定时',
    dsc_edit_timing: '编辑定时',
    dsc_week_full_mon: '周一',
    dsc_week_full_tue: '周二',
    dsc_week_full_wed: '周三',
    dsc_week_full_thu: '周四',
    dsc_week_full_fri: '周五',
    dsc_week_full_sat: '周六',
    dsc_week_full_sun: '周日',
    dsc_one_time: '仅一次',
    dsc_everyday: '每天',
    dsc_remark: '备注',
    dsc_less_than_20: '不超过20个字符',
    dsc_edit_success: '修改成功',
    dsc_create_success: '创建成功',
    dsc_no_timing: '暂无定时',
    dsc_error: '错误',
    dsc_am: '上午',
    dsc_pm: '下午',
  },
} as const;
