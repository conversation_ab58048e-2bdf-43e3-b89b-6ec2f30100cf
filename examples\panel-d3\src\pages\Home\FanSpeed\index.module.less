@import '@/variables.less';

.container {
  padding: 0 48rpx;
  margin-bottom: 128rpx;
  transition: opacity 0.4s @ease-in-out;
  :global(.ty-normal-slider-thumb) {
    &::after {
      content: '';
      position: absolute;
      left: 50%;
      height: 56rpx;
      width: 4rpx;
      background-color: #000;
      background: #bcc9d7;
      box-shadow: inset 0 -2rpx 4rpx #ffffff;
      transform: translateX(-50%);
    }
  }
  :global(.ty-normal-slider) {
    box-shadow: inset -6rpx -6rpx 12rpx rgba(255, 255, 255, 0.6),
      inset 4rpx 2rpx 8rpx rgba(164, 171, 191, 0.45);
  }
  &:global(.disabled) {
    opacity: 0.4;
  }
}

.descWrapper {
  margin-top: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.label {
  font-size: 24rpx;
  color: rgba(0, 0, 0, 0.9);
}

.value {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.9);
}
