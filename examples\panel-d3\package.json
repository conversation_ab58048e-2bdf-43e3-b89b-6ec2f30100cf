{"name": "typaneld3demo", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"lint": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "watch": "ray start --target=tuya", "build:tuya": "ray build --target=tuya", "start:tuya": "ray start --target=tuya"}, "dependencies": {"@ray-js/components-ty-input": "^0.0.5", "@ray-js/components-ty-slider": "^0.2.8", "@ray-js/components-ty-swipeout": "^0.0.7", "@ray-js/components-ty-time-picker": "^0.1.1", "@ray-js/ray-error-catch": "^0.0.20", "@ray-js/gesture": "^0.1.4", "@ray-js/panel-sdk": "^1.10.0", "@ray-js/ray": "^1.4.45", "@ray-js/tuya-dp-kit": "^0.0.8", "@reduxjs/toolkit": "^1.8.4", "ahooks": "^3.7.0", "clsx": "^1.2.1", "color": "^4.2.3", "js-base64": "^3.7.2", "moment": "^2.29.4", "react-redux": "^7", "redux-actions": "^2.6.5", "redux-observable": "^2.0.0", "redux": "^4.1.2", "redux-logger": "^3.0.6", "redux-thunk": "^2.4.0", "@tweenjs/tween.js": "^18.6.4", "three": "0.133.1"}, "devDependencies": {"@commitlint/cli": "^7.2.1", "@commitlint/config-conventional": "^9.0.1", "@ray-js/cli": "^1.4.45", "@types/lodash": "^4.14.182", "@types/react": "^17.0.24", "@types/react-dom": "^17.0.9", "@types/redux-logger": "^3.0.9", "core-js": "^3.29.1", "eslint-config-tuya-panel": "^0.4.1", "husky": "^1.2.0", "lint-staged": "^10.2.11", "prettier": "^2.7.1", "react": "^17.0.2", "react-dom": "^17.0.2", "typescript": "^4.4.3"}, "resolutions": {"follow-redirects": "1.15.6"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS --config commitlint.config.js", "pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "git add"], "*.{json,md,yml,yaml}": ["prettier --write", "git add"]}, "author": "", "license": "ISC"}