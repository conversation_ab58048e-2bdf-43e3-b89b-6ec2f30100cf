@import '@/variables.less';
@import '@/mixins.less';

.hide-scrollbar();

input {
  caret-color: @theme-color!important;
}

.hover-class__button-press-1 {
  opacity: 0.1;
}

.hover-class__button-press-2 {
  opacity: 0.2;
}

.hover-class__button-press-3 {
  opacity: 0.3;
}

.hover-class__button-press-4 {
  opacity: 0.4;
}

.hover-class__button-press-5 {
  opacity: 0.5;
}

.hover-class__button-press-6 {
  opacity: 0.6;
}

.hover-class__button-press-7 {
  opacity: 0.7;
}

.hover-class__button-press-8 {
  opacity: 0.8;
}

.hover-class__button-press-9 {
  opacity: 0.9;
}

.hover-class__button-press-10 {
  opacity: 1;
}

.ty-normal-slider-ticks {
  & > view {
    transform: none !important;
  }
}
