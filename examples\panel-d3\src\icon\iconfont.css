@font-face {
  font-family: "iconfontpanel"; /* Project id 3605973 */
  src: url('iconfont.woff2?t=1662636894207') format('woff2'),
       url('iconfont.woff?t=1662636894207') format('woff'),
       url('iconfont.ttf?t=1662636894207') format('truetype');
}

.iconfontpanel {
  font-family: "iconfontpanel" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-panel-delete:before {
  content: "\e61a";
}

.icon-panel-add:before {
  content: "\e606";
}

.icon-panel-close:before {
  content: "\e607";
}

.icon-panel-angleRight:before {
  content: "\e608";
}

.icon-panel-arrowRight:before {
  content: "\e60a";
}

.icon-panel-closeFill:before {
  content: "\e60b";
}

.icon-panel-auto:before {
  content: "\e60c";
}

.icon-panel-arrowLeft:before {
  content: "\e60d";
}

.icon-panel-light:before {
  content: "\e60e";
}

.icon-panel-moon:before {
  content: "\e60f";
}

.icon-panel-leaf:before {
  content: "\e610";
}

.icon-panel-manual:before {
  content: "\e611";
}

.icon-panel-more:before {
  content: "\e612";
}

.icon-panel-fan:before {
  content: "\e613";
}

.icon-panel-power:before {
  content: "\e614";
}

.icon-panel-reverse:before {
  content: "\e615";
}

.icon-panel-sun:before {
  content: "\e616";
}

.icon-panel-horizontal:before {
  content: "\e617";
}

.icon-panel-vertical:before {
  content: "\e618";
}

.icon-panel-temperature:before {
  content: "\e619";
}

